<!-- Booking Section -->
<div class="col" id="booking">
    <div class="booking-card sticky-top" style="top: 20px;">
        <h3 class="mb-3">Book your stay</h3>

        <?php
        // Display booking errors if any
        if (isset($_SESSION['booking_errors']) && !empty($_SESSION['booking_errors'])) {
            echo '<div class="alert alert-danger mb-3">';
            foreach ($_SESSION['booking_errors'] as $error) {
                echo '<p class="mb-0"><i class="bi bi-exclamation-circle-fill me-2"></i>' . htmlspecialchars($error) . '</p>';
            }
            echo '</div>';
            // Clear errors after displaying
            unset($_SESSION['booking_errors']);
        }
        ?>

        <div class="price-info mb-3">
            <span class="h4 fw-bold">$199</span> <span class="text-muted">/ night</span>
        </div>
        <form action="../book.php" method="post">
            <input type="hidden" name="cabin_id" value="1">
            <input type="hidden" id="check_in" name="check_in" required>
            <input type="hidden" id="check_out" name="check_out" required>

            <?php if (!isset($_SESSION['user_id'])): ?>
            <div class="mb-3">
                <label for="name" class="form-label">Your Name</label>
                <input type="text" class="form-control" id="name" name="name">
            </div>
            <?php endif; ?>

            <!-- Calendar Section -->
            <div class="mb-3">
                <p class="mb-2">Select your dates:</p>
                <div class="calendar-container">
                    <div class="date-picker-wrapper">
                        <input type="text" id="date-range-picker" class="form-control" placeholder="Select check-in and check-out dates" readonly>
                    </div>
                    <div class="selected-dates mt-2">
                        <div id="selected-dates-display" class="small text-muted">Select check-in and check-out dates</div>
                    </div>
                </div>
            </div>

            <!-- Flatpickr CSS -->
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
            <!-- Flatpickr Theme -->
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">

            <div class="mb-3">
                <label for="guests" class="form-label">Guests</label>
                <input type="number" class="form-control" id="guests" name="guests" min="1" max="6" value="2" required>
                <div class="form-text">Maximum 6 guests allowed</div>
            </div>


            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                <div class="form-text">We'll send your booking confirmation here</div>
            </div>

            <!-- Coupon Section -->
            <div class="mb-3">
                <label for="coupon" class="form-label">Coupon Code</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="coupon" name="coupon" placeholder="Enter coupon code">
                    <button type="button" class="btn btn-outline-secondary" id="apply-coupon">Apply</button>
                </div>
                <div id="coupon-message" class="form-text"></div>
            </div>

            <!-- Price Details Section -->
            <div class="price-details mb-3">
                <h5 class="mb-3">Price Details</h5>
                <div class="d-flex justify-content-between mb-2">
                    <div>$199 × <span id="num-nights">0</span> nights</div>
                    <div>$<span id="subtotal">0</span></div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <div>Cleaning fee</div>
                    <div>$50</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <div>Service fee</div>
                    <div>$45</div>
                </div>
                <div class="d-flex justify-content-between mb-2" id="extra-bed-fee-row" style="display: none;">
                    <div>Extra bed</div>
                    <div>$25</div>
                </div>
                <div class="d-flex justify-content-between mb-2" id="coupon-discount-row" style="display: none;">
                    <div>Coupon discount (20%)</div>
                    <div id="coupon-discount" class="text-success">-$0</div>
                </div>
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <div>Total</div>
                    <div>$<span id="total-price">95</span></div>
                </div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="extra_bed" name="extra_bed">
                <label class="form-check-label" for="extra_bed">Add extra bed (+$25)</label>
            </div>

            <button type="submit" class="btn btn-primary w-100 py-3">Reserve</button>
        </form>


        <!-- Calendar Styles -->
        <style>
            .calendar-container {
                font-size: 0.9rem;
            }

            .date-picker-wrapper {
                margin-bottom: 10px;
            }

            /* Customize Flatpickr */
            .flatpickr-day.flatpickr-disabled {
                text-decoration: line-through;
                color: #ccc;
            }

            .flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange {
                background: #007bff;
                border-color: #007bff;
            }

            .flatpickr-day.inRange {
                background: #cce5ff;
                border-color: #cce5ff;
                box-shadow: -5px 0 0 #cce5ff, 5px 0 0 #cce5ff;
            }

            @media (max-width: 767px) {
                .flatpickr-calendar {
                    width: 100%;
                    max-width: 350px;
                }
            }
        </style>

        <!-- Flatpickr Script -->
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

        <!-- Calendar Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Constants
                const PRICE_PER_NIGHT = 199;
                const CLEANING_FEE = 50;
                const SERVICE_FEE = 45;
                const EXTRA_BED_FEE = 25;

                // Global variables for coupon
                let couponApplied = false;
                let couponDiscount = 0;

                // Elements
                const checkInInput = document.getElementById('check_in');
                const checkOutInput = document.getElementById('check_out');
                const dateRangePicker = document.getElementById('date-range-picker');
                const selectedDatesDisplay = document.getElementById('selected-dates-display');
                const extraBedCheckbox = document.getElementById('extra_bed');
                const extraBedFeeRow = document.getElementById('extra-bed-fee-row');

                // Get unavailable dates from the database
                <?php
                // Connect to the database
                require_once($_SERVER['DOCUMENT_ROOT'] . '/includes/db.php');

                // Get the cabin ID from the hidden input
                $cabin_id = 1; // This should match the hidden input value

                // Query to get all reservations for this cabin
                $query = "SELECT start_date, end_date FROM reservations WHERE cabin_id = :cabin_id";
                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':cabin_id', $cabin_id, PDO::PARAM_INT);
                $stmt->execute();

                $bookedDates = [];

                // Process each reservation to get all dates between start and end
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $start = new DateTime($row['start_date']);
                    $end = new DateTime($row['end_date']);
                    $interval = new DateInterval('P1D'); // 1 day interval
                    $dateRange = new DatePeriod($start, $interval, $end);

                    // Add each date in the range to the bookedDates array
                    foreach ($dateRange as $date) {
                        $bookedDates[] = $date->format('Y-m-d');
                    }
                    // Add the end date as well (not included in the range)
                    $bookedDates[] = $end->format('Y-m-d');
                }

                // Convert PHP array to JavaScript array
                echo "const unavailableDates = " . json_encode($bookedDates) . ";";
                ?>


                // Initialize Flatpickr
                const flatpickrInstance = flatpickr(dateRangePicker, {
                    mode: 'range',
                    minDate: 'today',
                    dateFormat: 'Y-m-d',
                    disable: unavailableDates,
                    showMonths: 2,
                    static: true,
                    defaultDate: ['2025-05-01', '2025-05-10'], // Example default selection
                    onClose: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length === 2) {
                            const startDate = formatDate(selectedDates[0]);
                            const endDate = formatDate(selectedDates[1]);

                            // Update hidden inputs
                            checkInInput.value = startDate;
                            checkOutInput.value = endDate;

                            // Update display
                            updateSelectedDatesDisplay(startDate, endDate);
                            updatePriceDetails(selectedDates[0], selectedDates[1]);
                        }
                    }
                });

                // Event listeners
                if (extraBedCheckbox) {
                    extraBedCheckbox.addEventListener('change', function() {
                        const dates = flatpickrInstance.selectedDates;
                        if (dates.length === 2) {
                            updatePriceDetails(dates[0], dates[1]);
                        }
                    });
                }

                // Functions
                function updateSelectedDatesDisplay(startDate, endDate) {
                    if (startDate && endDate) {
                        const startFormatted = formatDisplayDate(startDate);
                        const endFormatted = formatDisplayDate(endDate);
                        selectedDatesDisplay.textContent = `${startFormatted} - ${endFormatted}`;
                    } else {
                        selectedDatesDisplay.textContent = 'Select check-in and check-out dates';
                    }
                }

                function updatePriceDetails(startDate, endDate) {
                    if (startDate && endDate) {
                        const nights = Math.round((endDate - startDate) / (1000 * 60 * 60 * 24));

                        // If price details elements exist
                        const numNightsEl = document.getElementById('num-nights');
                        const subtotalEl = document.getElementById('subtotal');
                        const totalPriceEl = document.getElementById('total-price');

                        if (numNightsEl) numNightsEl.textContent = nights;

                        const subtotal = nights * PRICE_PER_NIGHT;
                        if (subtotalEl) subtotalEl.textContent = subtotal;

                        let total = subtotal + CLEANING_FEE + SERVICE_FEE;

                        // Add extra bed fee if checked and elements exist
                        if (extraBedCheckbox && extraBedCheckbox.checked) {
                            if (extraBedFeeRow) extraBedFeeRow.style.display = 'flex';
                            total += EXTRA_BED_FEE;
                        } else if (extraBedFeeRow) {
                            extraBedFeeRow.style.display = 'none';
                        }

                        // Apply coupon discount if applicable
                        if (couponApplied && nights > 0) {
                            couponDiscount = Math.round(total * 0.2); // 20% discount
                            total -= couponDiscount;
                            document.getElementById('coupon-discount').textContent = `-$${couponDiscount}`;
                            document.getElementById('coupon-discount-row').style.display = 'flex';
                        } else {
                            couponDiscount = 0;
                            document.getElementById('coupon-discount-row').style.display = 'none';
                        }

                        if (totalPriceEl) totalPriceEl.textContent = total;
                    } else {
                        // Reset price details if elements exist
                        const numNightsEl = document.getElementById('num-nights');
                        const subtotalEl = document.getElementById('subtotal');
                        const totalPriceEl = document.getElementById('total-price');

                        if (numNightsEl) numNightsEl.textContent = '0';
                        if (subtotalEl) subtotalEl.textContent = '0';
                        if (totalPriceEl) totalPriceEl.textContent = (CLEANING_FEE + SERVICE_FEE);
                        if (extraBedFeeRow) extraBedFeeRow.style.display = 'none';
                        document.getElementById('coupon-discount-row').style.display = 'none';
                    }
                }

                function formatDate(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }

                function formatDisplayDate(dateString) {
                    const date = new Date(dateString);
                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                }

                // Initialize with default dates if they exist
                const defaultDates = flatpickrInstance.selectedDates;
                if (defaultDates.length === 2) {
                    const startDate = formatDate(defaultDates[0]);
                    const endDate = formatDate(defaultDates[1]);

                    checkInInput.value = startDate;
                    checkOutInput.value = endDate;

                    updateSelectedDatesDisplay(startDate, endDate);
                    updatePriceDetails(defaultDates[0], defaultDates[1]);
                }

                // Coupon functionality
                document.getElementById('apply-coupon').addEventListener('click', function() {
                    const couponInput = document.getElementById('coupon');
                    const couponMessage = document.getElementById('coupon-message');
                    const couponCode = couponInput.value.trim().toUpperCase();

                    if (couponCode === 'SAVE20') {
                        if (!couponApplied) {
                            couponApplied = true;
                            couponMessage.innerHTML = '<span class="text-success"><i class="bi bi-check-circle-fill me-1"></i>Coupon applied! 20% discount</span>';
                            couponMessage.style.backgroundColor = '#d4edda';
                            couponMessage.style.padding = '8px';
                            couponMessage.style.borderRadius = '4px';
                            couponMessage.style.border = '1px solid #c3e6cb';

                            // Recalculate prices
                            const dates = flatpickrInstance.selectedDates;
                            if (dates.length === 2) {
                                updatePriceDetails(dates[0], dates[1]);
                            }
                        } else {
                            couponMessage.innerHTML = '<span class="text-info">Coupon already applied</span>';
                        }
                    } else if (couponCode === '') {
                        couponMessage.innerHTML = '<span class="text-muted">Please enter a coupon code</span>';
                    } else {
                        couponMessage.innerHTML = '<span class="text-danger">Invalid coupon code</span>';
                    }
                });

                // Remove coupon when input is cleared
                document.getElementById('coupon').addEventListener('input', function() {
                    if (this.value.trim() === '' && couponApplied) {
                        couponApplied = false;
                        couponDiscount = 0;
                        document.getElementById('coupon-message').innerHTML = '';
                        document.getElementById('coupon-message').style.backgroundColor = '';
                        document.getElementById('coupon-message').style.padding = '';
                        document.getElementById('coupon-message').style.borderRadius = '';
                        document.getElementById('coupon-message').style.border = '';

                        // Recalculate prices
                        const dates = flatpickrInstance.selectedDates;
                        if (dates.length === 2) {
                            updatePriceDetails(dates[0], dates[1]);
                        }
                    }
                });
            });
        </script>
    </div>
</div>