<section class="video-section" id="video-experience">
    <div class="video-container">
        <video autoplay muted loop playsinline>
            <source src="../assets/video/NOTEBOOK_30SEG_V.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div class="overlay"></div>
    </div>
    
    <div class="video-content">
        <div class="content-wrapper">
            <h2 data-aos="fade-up" data-aos-duration="800" class="modern-title">Experience Tranquility</h2>
            <p data-aos="fade-up" data-aos-duration="800" data-aos-delay="200" class="modern-subtitle">Immerse yourself in the peaceful surroundings of our premium cabin locations</p>
            <div class="features" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
                <div class="feature-item-video-section">
                    <div class="icon-container">
                        <i class="fas fa-tree"></i>
                    </div>
                    <span>Natural Beauty</span>
                </div>
                <div class="feature-item-video-section">
                    <div class="icon-container">
                        <i class="fas fa-mountain"></i>
                    </div>
                    <span>Scenic Views</span>
                </div>
                <div class="feature-item-video-section">
                    <div class="icon-container">
                        <i class="fas fa-moon"></i>
                    </div>
                    <span>Peaceful Nights</span>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.video-section {
    position: relative;
    height: 40vh;
    min-height: 500px;
    overflow: hidden;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.9);
    transition: filter 0.5s ease;
}

.video-section:hover .video-container video {
    filter: brightness(1);
}

.video-container .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
    backdrop-filter: blur(1px);
}

.video-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-align: center;
}

.content-wrapper {
    max-width: 800px;
    padding: 0 30px;
}

.modern-title {
    font-size: 3.2rem;
    margin-bottom: 20px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    font-weight: 700;
    letter-spacing: -0.5px;
}

.modern-subtitle {
    font-size: 1.4rem;
    margin-bottom: 40px;
    text-shadow: 0 1px 8px rgba(0, 0, 0, 0.5);
    font-weight: 300;
    line-height: 1.5;
}

.features {
    display: flex;
    justify-content: center;
    gap: 50px;
    margin-top: 30px;
}

.feature-item-video-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease;
}

.feature-item-video-section:hover {
    transform: translateY(-5px);
}

.icon-container {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.feature-item-video-section:hover .icon-container {
    background: #fff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.icon-container i {
    font-size: 1.8rem;
    color: #2c3e50;
}

.feature-item-video-section span {
    color: #fff;
    font-weight: 600;
    font-size: 1.1rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    margin-top: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .video-section {
        height: 100vh;
    }
    
    .modern-title {
        font-size: 2.3rem;
    }
    
    .modern-subtitle {
        font-size: 1.1rem;
    }
    
    .features {
        gap: 25px;
    }
    
    .icon-container {
        width: 50px;
        height: 50px;
    }
    
    .icon-container i {
        font-size: 1.5rem;
    }
    
    .feature-item-video-section span {
        font-size: 1rem;
    }

    .feature-item-video-section {
        display: flex;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        padding: 20px;
        transition: transform 0.3s ease;
    }
}

@media (max-width: 480px) {
    .features {
        flex-direction: column;
        gap: 20px;
    }
    
    .feature-item-video-section {
        flex-direction: row;
        text-align: left;
        width: 100%;
        justify-content: flex-start;
    }
    
    .icon-container {
        margin-bottom: 0;
        margin-right: 15px;
    }
}
</style>