<?php
// <PERSON>'s Inspiration Cabin Section
?>

<section class="picassos-inspiration">
    <div class="container">
        <!-- Modern Header with Cabin Name -->
        <div class="modern-header" data-aos="fade-up">
            <h2><PERSON>'s Inspiration</h2>
            <p class="subtitle">Family Adventure Retreat</p>
        </div>
        
        <!-- Carousel Section -->
        <div class="carousel-container" data-aos="fade-up" data-aos-delay="100">
            <div id="inspirationCarousel" class="carousel slide" data-bs-ride="carousel">
                <!-- Indicators -->
                <div class="carousel-indicators">
                    <?php for($i = 0; $i < 12; $i++): ?>
                        <button type="button" data-bs-target="#inspirationCarousel" data-bs-slide-to="<?php echo $i; ?>" <?php echo ($i === 0) ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $i+1; ?>"></button>
                    <?php endfor; ?>
                </div>
                
                <!-- Slides -->
                <div class="carousel-inner">
                    <?php for($i = 1; $i <= 12; $i++): ?>
                        <div class="carousel-item <?php echo ($i === 1) ? 'active' : ''; ?>">
                            <img src="../assets/images/INSPIRATION/<?php echo $i; ?>.png" class="d-block w-100" alt="Picasso's Inspiration Cabin Image <?php echo $i; ?>">
                            <div class="carousel-caption d-none d-md-block">
                                <div class="caption-box"></div>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
                
                <!-- Controls -->
                <button class="carousel-control-prev" type="button" data-bs-target="#inspirationCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#inspirationCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>
        </div>
        
        <!-- Cabin Description -->
        <div class="cabin-description" data-aos="fade-up" data-aos-delay="200">
            <div class="row">
                <div class="col-md-12">
                    <h3>Family-Friendly Luxury</h3>
                    <p>A perfect blend of comfort and adventure for the whole family in this spacious retreat nestled in nature.</p>
                    
                    <div class="feature-highlights">
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-bed"></i></div>
                            <div class="feature-content">
                                <h4>Premium Bedrooms</h4>
                                <p>Downstairs features a king bedroom and a queen bedroom, each with their own private en-suite bath and custom tile walk-in showers.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-users"></i></div>
                            <div class="feature-content">
                                <h4>Spacious Loft</h4>
                                <p>Upstairs you'll find a queen bedroom with its own bathroom. The loft has 4 custom built-in twin bunks and a sofa sleeper.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-gamepad"></i></div>
                            <div class="feature-content">
                                <h4>Game Room</h4>
                                <p>The loft doubles as a game room with a foosball table and a retro video arcade table offering 60+ games including Mrs. Pacman, Galaga, Frogger, and Dig Dug.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-fire"></i></div>
                            <div class="feature-content">
                                <h4>Outdoor Entertainment</h4>
                                <p>Large covered deck with cozy seating around the outdoor gas log fireplace and dining table. Separate hot tub deck and fire pit area with Adirondack chairs.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-concierge-bell"></i></div>
                            <div class="feature-content">
                                <h4>Concierge Services</h4>
                                <p>Pre-order groceries, flowers, or book in-cabin massages. Pet-friendly for up to 2 pets. Queen double-high air bed available upon request for extra guests.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="booking-cta">
                        <a href="/picassos_inspiration" class="btn btn-primary">Book Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add some custom styles for this section -->
<style>
    .picassos-inspiration {
        padding: 80px 0;
        background-color: #f5f7fa;
        position: relative;
    }
    
    .picassos-inspiration .modern-header {
        text-align: center;
        margin-bottom: 50px;
    }
    
    .picassos-inspiration .modern-header h2 {
        font-size: 3.5rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 3px;
        position: relative;
        display: inline-block;
    }
    
    .picassos-inspiration .modern-header h2:after {
        content: '';
        position: absolute;
        width: 60px;
        height: 4px;
        background-color: #3498db;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .picassos-inspiration .modern-header .subtitle {
        font-size: 1.3rem;
        color: #555;
        font-weight: 400;
        margin-top: 20px;
    }
    
    .picassos-inspiration .carousel-container {
        margin-bottom: 60px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        border-radius: 12px;
        overflow: hidden;
    }
    
    .picassos-inspiration .carousel-item {
        position: relative;
    }
    
    .picassos-inspiration .carousel-item img {
        height: 600px;
        object-fit: cover;
        filter: brightness(0.9);
        transition: transform 0.5s ease;
    }
    
    .picassos-inspiration .carousel-item:hover img {
        transform: scale(1.03);
    }
    
    .picassos-inspiration .carousel-caption {
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
        text-align: left;
    }
    
    .picassos-inspiration .carousel-indicators {
        margin-bottom: 1rem;
    }
    
    .picassos-inspiration .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: rgba(255,255,255,0.5);
        border: none;
    }
    
    .picassos-inspiration .carousel-indicators button.active {
        background-color: #fff;
        transform: scale(1.2);
    }
    
    .picassos-inspiration .carousel-control-prev, 
    .picassos-inspiration .carousel-control-next {
        width: 10%;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .picassos-inspiration .carousel-container:hover .carousel-control-prev,
    .picassos-inspiration .carousel-container:hover .carousel-control-next {
        opacity: 0.8;
    }
    
    .picassos-inspiration .cabin-description {
        padding: 40px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    }
    
    .picassos-inspiration .cabin-description h3 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-weight: 700;
        font-size: 2.2rem;
        position: relative;
        padding-bottom: 15px;
    }
    
    .picassos-inspiration .cabin-description h3:after {
        content: '';
        position: absolute;
        width: 70px;
        height: 3px;
        background-color: #3498db;
        bottom: 0;
        left: 0;
    }
    
    .picassos-inspiration .feature-highlights {
        margin-top: 40px;
    }
    
    .picassos-inspiration .feature {
        margin-bottom: 30px;
        padding-bottom: 25px;
        border-bottom: 1px solid #ecf0f1;
        display: flex;
        align-items: flex-start;
        transition: transform 0.3s ease;
    }
    
    .picassos-inspiration .feature:hover {
        transform: translateY(-5px);
    }
    
    .picassos-inspiration .feature-icon {
        font-size: 1.8rem;
        color: #3498db;
        margin-right: 20px;
        background-color: rgba(52, 152, 219, 0.1);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
    
    .picassos-inspiration .feature-content {
        flex: 1;
    }
    
    .picassos-inspiration .feature h4 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }
    
    .picassos-inspiration .feature p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 0;
    }
    
    .picassos-inspiration .booking-cta {
        text-align: center;
        margin-top: 50px;
    }
    
    .picassos-inspiration .booking-cta .btn {
        padding: 15px 40px;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        background-color: #3498db;
        border: none;
        border-radius: 50px;
        transition: all 0.4s ease;
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    }
    
    .picassos-inspiration .booking-cta .btn:hover {
        background-color: #2980b9;
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(52, 152, 219, 0.4);
    }
    
    @media (max-width: 768px) {
        .picassos-inspiration .modern-header h2 {
            font-size: 2.5rem;
        }
        
        .picassos-inspiration .carousel-item img {
            height: 400px;
        }
        
        .picassos-inspiration .cabin-description {
            padding: 25px;
        }
        
        .picassos-inspiration .feature {
            flex-direction: column;
        }
        
        .picassos-inspiration .feature-icon {
            margin-bottom: 15px;
            margin-right: 0;
        }
    }
</style>