<div class="nav-container" style="position: relative; z-index: 50;">
    <nav class="modern-nav2">
        <div class="container d-flex justify-content-between align-items-center">
            <div class="logo">
                <a href="../index.php" class="animate__animated animate__fadeInLeft">
                    <img src="../assets/images/logo.png" alt="Picasso's Cabins Logo" style="max-height: 80px; width: auto;">
                </a>
            </div>
            
            <!-- Hamburger menu button for mobile -->
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileMenu" aria-controls="mobileMenu" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- Desktop menu -->
            <ul class="d-none d-lg-flex list-unstyled mb-0 animate__animated animate__fadeInRight">
                <li><a href="../index.php"><i class="fas fa-home me-1"></i> Home</a></li>
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li><a href="../dashboard.php"><i class="fas fa-bookmark me-1"></i> My Bookings</a></li>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                        <li><a href="../admin/view_bookings.php"><i class="fas fa-user-shield me-1"></i> Admin</a></li>
                    <?php endif; ?>
                    <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-1"></i> Logout</a></li>
                <?php else: ?>
                    <li><a href="../login.php"><i class="fas fa-sign-in-alt me-1"></i> Login</a></li>
                    <li><a href="../register.php"><i class="fas fa-user-plus me-1"></i> Register</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>
    
    <!-- Mobile sidebar menu -->
    <div class="offcanvas offcanvas-end z-index-20" tabindex="-1" id="mobileMenu" aria-labelledby="mobileMenuLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileMenuLabel">Menu</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="list-unstyled mobile-menu">
                <li><a href="../index.php"><i class="fas fa-home me-2"></i> Home</a></li>
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li><a href="../dashboard.php"><i class="fas fa-bookmark me-2"></i> My Bookings</a></li>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                        <li><a href="../admin/view_bookings.php"><i class="fas fa-user-shield me-2"></i> Admin</a></li>
                    <?php endif; ?>
                    <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                <?php else: ?>
                    <li><a href="../login.php"><i class="fas fa-sign-in-alt me-2"></i> Login</a></li>
                    <li><a href="../register.php"><i class="fas fa-user-plus me-2"></i> Register</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>

<style>
    /* Mobile menu styling */
    .navbar-toggler {
        border: none;
        background: transparent;
        font-size: 1.5rem;
        color: #fff;
        padding: 8px 12px;
        transition: all 0.3s ease;
    }
    
    .navbar-toggler:hover {
        color: #f8f9fa;
        transform: scale(1.1);
    }
    
    .mobile-menu li {
        margin-bottom: 15px;
    }
    
    .mobile-menu li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        border-radius: 5px;
    }
    
    .mobile-menu li a:hover {
        background-color: #f8f9fa;
        color: #2c3e50;
    }
</style>
