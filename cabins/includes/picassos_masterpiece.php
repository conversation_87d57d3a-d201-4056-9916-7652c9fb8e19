<?php
// <PERSON>'s Masterpiece Cabin Section
?>

<section class="picassos-masterpiece">
    <div class="container">
        <!-- Modern Header with Cabin Name -->
        <div class="modern-header" data-aos="fade-up">
            <h2><PERSON>'s Masterpiece</h2>
            <p class="subtitle">Ultra-Modern Luxury</p>
        </div>
        
        <!-- Carousel Section -->
        <div class="carousel-container" data-aos="fade-up" data-aos-delay="100">
            <div id="masterpieceCarousel" class="carousel slide" data-bs-ride="carousel">
                <!-- Indicators -->
                <div class="carousel-indicators">
                    <?php for($i = 0; $i < 14; $i++): ?>
                        <button type="button" data-bs-target="#masterpieceCarousel" data-bs-slide-to="<?php echo $i; ?>" <?php echo ($i === 0) ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $i+1; ?>"></button>
                    <?php endfor; ?>
                </div>
                
                <!-- Slides -->
                <div class="carousel-inner">
                    <?php for($i = 1; $i <= 14; $i++): ?>
                        <div class="carousel-item <?php echo ($i === 1) ? 'active' : ''; ?>">
                            <img src="../assets/images/MASTERPIECE/<?php echo $i; ?>.png" class="d-block w-100" alt="Picasso's Masterpiece Cabin Image <?php echo $i; ?>">
                            <div class="carousel-caption d-none d-md-block">
                                <div class="caption-box"></div>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
                
                <!-- Controls -->
                <button class="carousel-control-prev" type="button" data-bs-target="#masterpieceCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#masterpieceCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>
        </div>
        
        <!-- Cabin Description -->
        <div class="cabin-description" data-aos="fade-up" data-aos-delay="200">
            <div class="row">
                <div class="col-md-12">
                    <h3>Ultra-Modern Forest Retreat</h3>
                    <p>A true work of art nestled creekside in the forest with panoramic views and luxury amenities for the whole family.</p>
                    
                    <div class="feature-highlights">
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-home"></i></div>
                            <div class="feature-content">
                                <h4>Spectacular Design</h4>
                                <p>Newly constructed vacation home with ultra-modern design, sky-high ceilings, and panoramic views of towering pines from every room.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-utensils"></i></div>
                            <div class="feature-content">
                                <h4>Gourmet Kitchen</h4>
                                <p>Spacious kitchen with stainless steel appliances, beautiful granite countertops, and every modern cooking tool. Dining table seats 10 plus additional bar seating.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-bed"></i></div>
                            <div class="feature-content">
                                <h4>Luxury Accommodations</h4>
                                <p>Sleeps up to 17 people with 4 king bedrooms and 9 twin beds in the upstairs bunk room. Each bedroom has a private en-suite bathroom with walk-in showers.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-gamepad"></i></div>
                            <div class="feature-content">
                                <h4>Entertainment Paradise</h4>
                                <p>Loft game room with pool table and vintage arcade machine. Living room features giant flat screen above the fireplace with plush leather furnishings.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-hot-tub"></i></div>
                            <div class="feature-content">
                                <h4>Outdoor Oasis</h4>
                                <p>Spacious covered back deck with fireplace, TV, dining table and gas grill. Private hot tub cabana next to a beautiful creek and outdoor playground for kids.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="booking-cta">
                        <a href="/picassos_masterpiece" class="btn btn-primary">Book Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add some custom styles for this section -->
<style>
    .picassos-masterpiece {
        padding: 80px 0;
        background-color: #f5f7fa;
        position: relative;
    }
    
    .picassos-masterpiece .modern-header {
        text-align: center;
        margin-bottom: 50px;
    }
    
    .picassos-masterpiece .modern-header h2 {
        font-size: 3.5rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 3px;
        position: relative;
        display: inline-block;
    }
    
    .picassos-masterpiece .modern-header h2:after {
        content: '';
        position: absolute;
        width: 60px;
        height: 4px;
        background-color: #7fb77e;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .picassos-masterpiece .modern-header .subtitle {
        font-size: 1.3rem;
        color: #555;
        font-weight: 400;
        margin-top: 20px;
    }
    
    .picassos-masterpiece .carousel-container {
        margin-bottom: 60px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        border-radius: 12px;
        overflow: hidden;
    }
    
    .picassos-masterpiece .carousel-item {
        position: relative;
    }
    
    .picassos-masterpiece .carousel-item img {
        height: 600px;
        object-fit: cover;
        filter: brightness(0.9);
        transition: transform 0.5s ease;
    }
    
    .picassos-masterpiece .carousel-item:hover img {
        transform: scale(1.03);
    }
    
    .picassos-masterpiece .carousel-caption {
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
        text-align: left;
    }
    
    .picassos-masterpiece .carousel-indicators {
        margin-bottom: 1rem;
    }
    
    .picassos-masterpiece .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: rgba(255,255,255,0.5);
        border: none;
    }
    
    .picassos-masterpiece .carousel-indicators button.active {
        background-color: #fff;
        transform: scale(1.2);
    }
    
    .picassos-masterpiece .carousel-control-prev, 
    .picassos-masterpiece .carousel-control-next {
        width: 10%;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .picassos-masterpiece .carousel-container:hover .carousel-control-prev,
    .picassos-masterpiece .carousel-container:hover .carousel-control-next {
        opacity: 0.8;
    }
    
    .picassos-masterpiece .cabin-description {
        padding: 40px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    }
    
    .picassos-masterpiece .cabin-description h3 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-weight: 700;
        font-size: 2.2rem;
        position: relative;
        padding-bottom: 15px;
    }
    
    .picassos-masterpiece .cabin-description h3:after {
        content: '';
        position: absolute;
        width: 70px;
        height: 3px;
        background-color: #7fb77e;
        bottom: 0;
        left: 0;
    }
    
    .picassos-masterpiece .feature-highlights {
        margin-top: 40px;
    }
    
    .picassos-masterpiece .feature {
        margin-bottom: 30px;
        padding-bottom: 25px;
        border-bottom: 1px solid #ecf0f1;
        display: flex;
        align-items: flex-start;
        transition: transform 0.3s ease;
    }
    
    .picassos-masterpiece .feature:hover {
        transform: translateY(-5px);
    }
    
    .picassos-masterpiece .feature-icon {
        font-size: 1.8rem;
        color: #7fb77e;
        margin-right: 20px;
        background-color: rgba(52, 152, 219, 0.1);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
    
    .picassos-masterpiece .feature-content {
        flex: 1;
    }
    
    .picassos-masterpiece .feature h4 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }
    
    .picassos-masterpiece .feature p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 0;
    }
    
    .picassos-masterpiece .booking-cta {
        text-align: center;
        margin-top: 50px;
    }
    
    .picassos-masterpiece .booking-cta .btn {
        padding: 15px 40px;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        background-color:rgb(94, 133, 93);
        border: none;
        border-radius: 50px;
        transition: all 0.4s ease;
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    }
    
    .picassos-masterpiece .booking-cta .btn:hover {
        background-color: #7fb77e;
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(52, 152, 219, 0.4);
    }
    
    @media (max-width: 768px) {
        .picassos-masterpiece .modern-header h2 {
            font-size: 2.5rem;
        }
        
        .picassos-masterpiece .carousel-item img {
            height: 400px;
        }
        
        .picassos-masterpiece .cabin-description {
            padding: 25px;
        }
        
        .picassos-masterpiece .feature {
            flex-direction: column;
        }
        
        .picassos-masterpiece .feature-icon {
            margin-bottom: 15px;
            margin-right: 0;
        }
    }
</style>