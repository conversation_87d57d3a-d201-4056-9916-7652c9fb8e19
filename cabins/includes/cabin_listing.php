<?php
// This file contains the cabin listing section of the cabin listing page
?>
<div class="container" id="cabins">
    <h2 class="text-center my-5" data-aos="fade-up">Available Cabins</h2>
    
    <?php if (isset($error)): ?>
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>
    
    <?php if (empty($cabins)): ?>
        <div class="text-center py-5">
            <i class="fas fa-home fa-4x text-muted mb-3"></i>
            <h3 class="text-muted">No cabins available at the moment</h3>
            <p class="text-muted">Please check back later for new listings</p>
        </div>
    <?php else: ?>
        <div class="cabin-list row">
            <?php foreach ($cabins as $cabin): ?>
                <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="<?php echo $loop_index = ($loop_index ?? 0) + 100; ?>">
                    <div class="cabin-card shadow-sm h-100 rounded overflow-hidden">
                        <div class="position-relative cabin-image-container">
                            <?php if (!empty($cabin['image'])): ?>
                                <img src="../assets/images/<?php echo htmlspecialchars($cabin['image']); ?>" alt="<?php echo htmlspecialchars($cabin['title']); ?>" class="img-fluid">
                            <?php else: ?>
                                <img src="../assets/images/default-cabin.jpg" alt="Default cabin image" class="img-fluid">
                            <?php endif; ?>
                            <div class="cabin-price-badge">
                                <span class="badge bg-primary rounded-pill px-3 py-2 fs-6">
                                    <i class="fas fa-tag me-1"></i> $<?php echo htmlspecialchars($cabin['price_per_night']); ?>/night
                                </span>
                            </div>
                        </div>
                        
                        <div class="cabin-card-content p-4">
                            <h3 class="mb-2 fw-bold"><?php echo htmlspecialchars($cabin['title']); ?></h3>
                            <p class="cabin-location mb-3">
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                <?php echo htmlspecialchars($cabin['location']); ?>
                            </p>
                            <div class="cabin-features mb-3">
                                <span class="badge bg-light text-dark me-2 mb-1"><i class="fas fa-bed me-1"></i> Cozy</span>
                                <span class="badge bg-light text-dark me-2 mb-1"><i class="fas fa-wifi me-1"></i> WiFi</span>
                                <span class="badge bg-light text-dark me-2 mb-1"><i class="fas fa-mountain me-1"></i> View</span>
                            </div>
                            <p class="cabin-description"><?php echo nl2br(htmlspecialchars(substr($cabin['description'], 0, 100))); ?>...</p>
                            <a href="../view.php?id=<?php echo $cabin['id']; ?>" class="btn btn-outline-primary w-100 mt-3">
                                <i class="fas fa-info-circle me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>