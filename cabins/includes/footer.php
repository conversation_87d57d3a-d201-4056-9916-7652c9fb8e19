<!-- Footer Section -->
<footer class="modern-footer">
    <div class="container">
        <div class="row justify-content-between">
            <!-- Company Info -->
            <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                <h5 class="footer-heading"><PERSON><PERSON><PERSON>'s</h5>
                <p class="footer-text">Experience luxury in nature with our handpicked selection of beautiful cabins in stunning locations.</p>
                <div class="contact-info">
                    <p><i class="fas fa-map-marker-alt"></i> 123 Forest Drive, Hochatown, OK</p>
                    <p><i class="fas fa-phone"></i> +**************** </p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>

            
            <!-- Newsletter -->
            <div class="col-lg-3 col-md-6 mb-4 mb-md-0">
                <h5 class="footer-heading">Stay Updated</h5>
               
                <div class="social-links mt-3">
                    <a href="https://www.facebook.com/share/1WfdE34PUE/" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                    <a href="https://www.instagram.com/picassoscabins?igsh=MWF2MnRxYzE4OTdmeQ==" class="social-icon"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Copyright -->
    <div class="footer-bottom">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="copyright-text">© 2025 Cabins Picasso's. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="terms-links">
                        <a href="#">Privacy Policy</a> | 
                        <a href="#">Terms of Service</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Footer Styles -->
<style>
    .modern-footer {
        background-color: #2c3e50;
        color: #ecf0f1;
        padding-top: 70px;
        position: relative;
    }
    
    .footer-heading {
        color: #fff;
        font-weight: 600;
        margin-bottom: 25px;
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 10px;
    }
    
    .footer-heading:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 50px;
        height: 3px;
        background-color: #e74c3c;
    }
    
    .footer-text {
        color: #bdc3c7;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    
    .contact-info p {
        margin-bottom: 10px;
        color: #bdc3c7;
    }
    
    .contact-info i {
        color: #e74c3c;
        width: 25px;
        margin-right: 10px;
    }
    
    .footer-links {
        list-style: none;
        padding-left: 0;
    }
    
    .footer-links li {
        margin-bottom: 12px;
    }
    
    .footer-links a {
        color: #bdc3c7;
        text-decoration: none;
        transition: color 0.3s ease;
        position: relative;
        padding-left: 15px;
    }
    
    .footer-links a:before {
        content: '›';
        position: absolute;
        left: 0;
        color: #e74c3c;
    }
    
    .footer-links a:hover {
        color: #fff;
    }
    
    .newsletter-form .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        color: #fff;
        padding: 10px 15px;
        border-radius: 4px 0 0 4px;
    }
    
    .newsletter-form .form-control::placeholder {
        color: #bdc3c7;
    }
    
    .newsletter-form .btn {
        background-color: #e74c3c;
        border: none;
        padding: 10px 15px;
        border-radius: 0 4px 4px 0;
    }
    
    .social-links {
        display: flex;
        gap: 15px;
    }
    
    .social-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: rgba(255, 255, 255, 0.1);
        color: #ecf0f1;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .social-icon:hover {
        background-color: #e74c3c;
        color: #fff;
        transform: translateY(-3px);
    }
    
    .footer-bottom {
        background-color: rgba(0, 0, 0, 0.2);
        padding: 20px 0;
        margin-top: 50px;
    }
    
    .copyright-text {
        margin-bottom: 0;
        color: #bdc3c7;
    }
    
    .terms-links a {
        color: #bdc3c7;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .terms-links a:hover {
        color: #fff;
    }
    
    @media (max-width: 768px) {
        .modern-footer {
            padding-top: 50px;
        }
        
        .footer-heading {
            margin-top: 20px;
        }
        
        .footer-bottom {
            text-align: center;
        }
        
        .terms-links {
            margin-top: 10px;
            text-align: center;
        }
    }
</style>

<script src="../assets/js/app.js"></script>
<!-- Modern UI Libraries JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<script>
    // Initialize AOS animation library
    AOS.init();
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const nav = document.querySelector('.modern-nav');
        if (window.scrollY > 100) {
            nav.style.background = 'rgba(44, 62, 80, 0.95)';
            nav.style.padding = '0.5rem 0';
        } else {
            nav.style.background = 'rgba(44, 62, 80, 0.9)';
            nav.style.padding = '1rem 0';
        }
    });
    
    // Smooth scroll to cabins section
    document.querySelector('.explore-btn').addEventListener('click', function(e) {
        e.preventDefault();
        document.querySelector('#cabins').scrollIntoView({ behavior: 'smooth' });
    });
</script>
</body>
</html>