<section class="cabin-features">
    <div class="container">
        <h2>Cabin Features & Amenities</h2>
        
        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="feature-content">
                    <h3>Modern Kitchen</h3>
                    <p>The ultra-modern kitchen features stainless built-in stainless appliances and everything you will need to prepare a gourmet meal.</p>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-bed"></i>
                </div>
                <div class="feature-content">
                    <h3>Comfortable Bedrooms</h3>
                    <p>Both bedrooms offer one king bed, one queen bed, a futon, luxury linens, wood blinds and a flat screen satellite TV.</p>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-hot-tub"></i>
                </div>
                <div class="feature-content">
                    <h3>Outdoor Relaxation</h3>
                    <p>After a day at the lake, you can grill out on the back deck and enjoy your dinner at the large dining table on the covered patio, while sitting high above a panoramic forest canopy backdrop.</p>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="feature-content">
                    <h3>Outdoor Activities</h3>
                    <p>Don't forget the s'mores ingredients to enjoy around the furnished campfire pit.</p>
                </div>
            </div>
            
        </div>
        
    </div>
</section>

<style>
    .cabin-features {
        padding: 60px 0;
        background-color: #f8f9fa;
    }
    
    .cabin-features h2 {
        text-align: center;
        margin-bottom: 40px;
        color: #2c3e50;
        font-size: 32px;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
    }
    
    .feature-item {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        padding: 20px;
        transition: transform 0.3s ease;
    }
    
    .feature-item:hover {
        transform: translateY(-5px);
    }
    
    .feature-icon {
        font-size: 24px;
        color: #27ae60;
        margin-right: 15px;
        min-width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .feature-content h3 {
        margin-top: 0;
        color: #34495e;
        font-size: 20px;
        margin-bottom: 10px;
    }
    
    .feature-content p {
        color: #7f8c8d;
        line-height: 1.6;
        margin: 0;
    }
    
    .cta-button {
        text-align: center;
    }
    
    .btn-primary {
        background-color: #27ae60;
        color: white;
        padding: 12px 24px;
        border-radius: 4px;
        text-decoration: none;
        font-weight: bold;
        display: inline-block;
        transition: background-color 0.3s ease;
    }
    
    .btn-primary:hover {
        background-color: #219653;
    }
    
    @media (max-width: 768px) {
        .features-grid {
            grid-template-columns: 1fr;
        }
        
        .feature-item {
            flex-direction: column;
        }
        
        .feature-icon {
            margin-right: 0;
            margin-bottom: 15px;
            font-size: 30px;
        }
    }
</style>