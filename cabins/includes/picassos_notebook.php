<?php
// <PERSON>'s Notebook Cabin Section
?>

<section class="picassos-notebook">
    <div class="container">
        <!-- Modern Header with Cabin Name -->
        <div class="modern-header" data-aos="fade-up">
            <h2><PERSON>'s Notebook</h2>
            <p class="subtitle">Luxury Meets Nature</p>
        </div>
        
        <!-- Carousel Section -->
        <div class="carousel-container" data-aos="fade-up" data-aos-delay="100">
            <div id="notebookCarousel" class="carousel slide" data-bs-ride="carousel">
                <!-- Indicators -->
                <div class="carousel-indicators">
                    <?php for($i = 0; $i < 15; $i++): ?>
                        <button type="button" data-bs-target="#notebookCarousel" data-bs-slide-to="<?php echo $i; ?>" <?php echo ($i === 0) ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $i+1; ?>"></button>
                    <?php endfor; ?>
                </div>
                
                <!-- Slides -->
                <div class="carousel-inner">
                    <?php for($i = 1; $i <= 15; $i++): ?>
                        <div class="carousel-item <?php echo ($i === 1) ? 'active' : ''; ?>">
                            <img src="../assets/images/NOTEBOOK/<?php echo $i; ?>.png" class="d-block w-100" alt="Picasso's Notebook Cabin Image <?php echo $i; ?>" onclick="openImageModal('../assets/images/NOTEBOOK/<?php echo $i; ?>.png', <?php echo $i; ?>, 15)">
                            <div class="carousel-caption d-none d-md-block">
                                <div class="caption-box"></div>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
                
                <!-- Controls -->
                <button class="carousel-control-prev" type="button" data-bs-target="#notebookCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#notebookCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>
        </div>
        
        <!-- Cabin Description -->
        <div class="cabin-description" data-aos="fade-up" data-aos-delay="200">
            <div class="row">
                <div class="col-md-12">
                    <h3>Wilderness Luxury Redefined</h3>
                    <p>Modern comfort meets natural beauty in this exceptional retreat designed for both relaxation and adventure.</p>
                    
                    <div class="feature-highlights">
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-utensils"></i></div>
                            <div class="feature-content">
                                <h4>Designer Kitchen</h4>
                                <p>Gourmet kitchen with premium appliances. Just 2 minutes from Hochatown's restaurants, wineries, and Beavers Bend State Park.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-bed"></i></div>
                            <div class="feature-content">
                                <h4>Premium Bedrooms</h4>
                                <p>King and queen beds with luxury linens. Flat-screen TVs and elegant bathrooms with granite counters.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-tree"></i></div>
                            <div class="feature-content">
                                <h4>Panoramic Deck</h4>
                                <p>Hot tub, dining area, and lounge seating overlooking forest views. Outdoor TV and gas fireplace for romantic evenings.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-hiking"></i></div>
                            <div class="feature-content">
                                <h4>Adventure Access</h4>
                                <p>Campfire pit for s'mores. Minutes from hiking, fishing, swimming, and horseback riding trails.</p>
                            </div>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon"><i class="fas fa-concierge-bell"></i></div>
                            <div class="feature-content">
                                <h4>Concierge Services</h4>
                                <p>Pre-order groceries, flowers, or book in-cabin massages. Additional air bed available for extra guests.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="booking-cta">
                        <a href="/picassos_notebook" class="btn btn-primary">Book Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Picasso's Notebook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalImage" src="" class="img-fluid" alt="Full size image">
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-secondary" id="prevImageBtn"><i class="fas fa-chevron-left"></i> Previous</button>
                <span id="imageCounter">Image 1 of 15</span>
                <button type="button" class="btn btn-secondary" id="nextImageBtn">Next <i class="fas fa-chevron-right"></i></button>
            </div>
        </div>
    </div>
</div>

<!-- Add some custom styles for this section -->
<style>
    .picassos-notebook {
        padding: 80px 0;
        background-color: #f8f9fa;
        position: relative;
    }
    
    .modern-header {
        text-align: center;
        margin-bottom: 50px;
    }
    
    .modern-header h2 {
        font-size: 3.5rem;
        font-weight: 800;
        color: #1a1a2e;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 3px;
        position: relative;
        display: inline-block;
    }
    
    .modern-header h2:after {
        content: '';
        position: absolute;
        width: 60px;
        height: 4px;
        background-color: #e74c3c;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .modern-header .subtitle {
        font-size: 1.3rem;
        color: #555;
        font-weight: 400;
        margin-top: 20px;
    }
    
    .carousel-container {
        margin-bottom: 60px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        border-radius: 12px;
        overflow: hidden;
    }
    
    .carousel-item {
        position: relative;
    }
    
    .carousel-item img {
        height: 600px;
        object-fit: cover;
        filter: brightness(0.9);
        transition: transform 0.5s ease;
    }
    
    .carousel-item:hover img {
        transform: scale(1.03);
    }
    
    .carousel-caption {
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
        text-align: left;
    }
    
    .carousel-indicators {
        margin-bottom: 1rem;
    }
    
    .picassos-notebook .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: rgba(255,255,255,0.5);
        border: none;
    }
    
    .carousel-indicators button.active {
        background-color: #fff;
        transform: scale(1.2);
    }
    
    .carousel-control-prev, .carousel-control-next {
        width: 10%;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .carousel-container:hover .carousel-control-prev,
    .carousel-container:hover .carousel-control-next {
        opacity: 0.8;
    }
    
    .cabin-description {
        padding: 40px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    }
    
    .cabin-description h3 {
        color: #1a1a2e;
        margin-bottom: 20px;
        font-weight: 700;
        font-size: 2.2rem;
        position: relative;
        padding-bottom: 15px;
    }
    
    .cabin-description h3:after {
        content: '';
        position: absolute;
        width: 70px;
        height: 3px;
        background-color: #e74c3c;
        bottom: 0;
        left: 0;
    }
    
    .feature-highlights {
        margin-top: 40px;
    }
    
    .feature {
        margin-bottom: 30px;
        padding-bottom: 25px;
        border-bottom: 1px solid #ecf0f1;
        display: flex;
        align-items: flex-start;
        transition: transform 0.3s ease;
    }
    
    .feature:hover {
        transform: translateY(-5px);
    }
    
    .feature-icon {
        font-size: 1.8rem;
        color: #e74c3c;
        margin-right: 20px;
        background-color: rgba(231, 76, 60, 0.1);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
    
    .feature-content {
        flex: 1;
    }
    
    .feature h4 {
        color: #1a1a2e;
        font-weight: 600;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }
    
    .feature p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 0;
    }
    
    .booking-cta {
        text-align: center;
        margin-top: 50px;
    }
    
    .booking-cta .btn {
        padding: 15px 40px;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        background-color: #e74c3c;
        border: none;
        border-radius: 50px;
        transition: all 0.4s ease;
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    }
    
    .booking-cta .btn:hover {
        background-color: #c0392b;
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(231, 76, 60, 0.4);
    }
    
    @media (max-width: 768px) {
        .modern-header h2 {
            font-size: 2.5rem;
        }
        
        .carousel-item img {
            height: 400px;
        }
        
        .cabin-description {
            padding: 25px;
        }
        
        .feature {
            flex-direction: column;
        }
        
        .feature-icon {
            margin-bottom: 15px;
            margin-right: 0;
        }
    }
    /* Modal Styles */
    #imageModal .modal-content {
        background-color: rgba(255, 255, 255, 0.95);
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    
    #imageModal .modal-header {
        border-bottom: 1px solid rgba(0,0,0,0.1);
        padding: 15px 20px;
    }
    
    #imageModal .modal-title {
        font-weight: 600;
        color: #1a1a2e;
    }
    
    #imageModal .modal-body {
        padding: 0;
        background-color: rgba(0,0,0,0.02);
        position: relative;
    }
    
    #modalImage {
        max-height: 80vh;
        object-fit: contain;
    }
    
    #imageModal .modal-footer {
        border-top: 1px solid rgba(0,0,0,0.1);
        padding: 15px 20px;
    }
    
    #imageCounter {
        font-size: 0.9rem;
        color: #555;
    }
    
    #prevImageBtn, #nextImageBtn {
        background-color: #e74c3c;
        border: none;
        padding: 8px 15px;
        transition: all 0.3s ease;
    }
    
    #prevImageBtn:hover, #nextImageBtn:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
    }
    
    .carousel-item img {
        cursor: pointer;
    }
</style>

<script>
    // Function to open the image modal
    function openImageModal(imageSrc, currentIndex, totalImages) {
        // Set the image source
        document.getElementById('modalImage').src = imageSrc;
        
        // Update the counter
        document.getElementById('imageCounter').textContent = 'Image ' + currentIndex + ' of ' + totalImages;
        
        // Store current index and total in data attributes for navigation
        const modal = document.getElementById('imageModal');
        modal.setAttribute('data-current-index', currentIndex);
        modal.setAttribute('data-total-images', totalImages);
        
        // Initialize the Bootstrap modal
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }
    
    // Add event listeners when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get modal elements
        const modal = document.getElementById('imageModal');
        const prevBtn = document.getElementById('prevImageBtn');
        const nextBtn = document.getElementById('nextImageBtn');
        
        // Previous button click handler
        prevBtn.addEventListener('click', function() {
            const currentIndex = parseInt(modal.getAttribute('data-current-index'));
            const totalImages = parseInt(modal.getAttribute('data-total-images'));
            
            // Calculate the previous index (with wrap-around)
            let prevIndex = currentIndex - 1;
            if (prevIndex < 1) prevIndex = totalImages;
            
            // Update the modal image
            const newSrc = '../assets/images/NOTEBOOK/' + prevIndex + '.png';
            document.getElementById('modalImage').src = newSrc;
            
            // Update the counter and stored index
            document.getElementById('imageCounter').textContent = 'Image ' + prevIndex + ' of ' + totalImages;
            modal.setAttribute('data-current-index', prevIndex);
        });
        
        // Next button click handler
        nextBtn.addEventListener('click', function() {
            const currentIndex = parseInt(modal.getAttribute('data-current-index'));
            const totalImages = parseInt(modal.getAttribute('data-total-images'));
            
            // Calculate the next index (with wrap-around)
            let nextIndex = currentIndex + 1;
            if (nextIndex > totalImages) nextIndex = 1;
            
            // Update the modal image
            const newSrc = '../assets/images/NOTEBOOK/' + nextIndex + '.png';
            document.getElementById('modalImage').src = newSrc;
            
            // Update the counter and stored index
            document.getElementById('imageCounter').textContent = 'Image ' + nextIndex + ' of ' + totalImages;
            modal.setAttribute('data-current-index', nextIndex);
        });
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(event) {
            // Only process if the modal is open
            if (modal.classList.contains('show')) {
                if (event.key === 'ArrowLeft') {
                    prevBtn.click();
                } else if (event.key === 'ArrowRight') {
                    nextBtn.click();
                } else if (event.key === 'Escape') {
                    bootstrap.Modal.getInstance(modal).hide();
                }
            }
        });
    });
</script>