<?php
// DB connection is already included in dashboard.php

// User must be logged in to view their bookings
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Fetch user's bookings with cabin details
try {
    $stmt = $pdo->prepare('SELECT r.*, c.title, c.location, c.price_per_night, c.image 
                          FROM reservations r 
                          JOIN cabins c ON r.cabin_id = c.id 
                          WHERE r.user_id = ? 
                          ORDER BY r.start_date');
    $stmt->execute([$user_id]);
    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Bookings - Picasso's Cabins</title>
    <link rel="icon" href="./assets/images/logo.png" type="image/png">
    <link rel="stylesheet" href="./assets/css/style.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>Picasso's Cabins</h1>
            </div>
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="dashboard.php">My Bookings</a></li>
                <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                    <li><a href="admin/view_bookings.php">Admin</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <h2>My Bookings</h2>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if (empty($bookings)): ?>
            <p>You don't have any bookings yet. <a href="index.php">Browse cabins</a> to make a reservation.</p>
        <?php else: ?>
            <div class="bookings-list">
                <table>
                    <thead>
                        <tr>
                            <th>Cabin</th>
                            <th>Location</th>
                            <th>Check-in</th>
                            <th>Check-out</th>
                            <th>Nights</th>
                            <th>Total Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($bookings as $booking): 
                            // Calculate number of nights and total price
                            $start = new DateTime($booking['start_date']);
                            $end = new DateTime($booking['end_date']);
                            $nights = $end->diff($start)->days;
                            $total_price = $nights * $booking['price_per_night'];
                        ?>
                            <tr>
                                <td>
                                    <a href="./cabins/view.php?id=<?php echo $booking['cabin_id']; ?>">
                                        <?php echo htmlspecialchars($booking['title']); ?>
                                    </a>
                                </td>
                                <td><?php echo htmlspecialchars($booking['location']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($booking['start_date'])); ?></td>
                                <td><?php echo date('M d, Y', strtotime($booking['end_date'])); ?></td>
                                <td><?php echo $nights; ?></td>
                                <td>$<?php echo number_format($total_price, 2); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>