<?php
// DB connection is already included in admin/view_bookings.php

// Ensure admin access
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: ../index.php');
    exit();
}

// Handle delete reservation
if (isset($_POST['delete_reservation'])) {
    $reservation_id = $_POST['reservation_id'];
    try {
        $stmt = $pdo->prepare('DELETE FROM reservations WHERE id = ?');
        $stmt->execute([$reservation_id]);
        $success_message = 'Reservation deleted successfully.';
    } catch (PDOException $e) {
        $error = 'Error deleting reservation: ' . $e->getMessage();
    }
}

// Handle update reservation
if (isset($_POST['update_reservation'])) {
    $reservation_id = $_POST['reservation_id'];
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];
    
    try {
        $stmt = $pdo->prepare('UPDATE reservations SET start_date = ?, end_date = ? WHERE id = ?');
        $stmt->execute([$start_date, $end_date, $reservation_id]);
        $success_message = 'Reservation updated successfully.';
    } catch (PDOException $e) {
        $error = 'Error updating reservation: ' . $e->getMessage();
    }
}

// Fetch all bookings with user and cabin details
try {
    $stmt = $pdo->query('SELECT r.*, c.title, c.location, c.price_per_night, u.name as user_name, u.email as user_email 
                          FROM reservations r 
                          JOIN cabins c ON r.cabin_id = c.id 
                          JOIN users u ON r.user_id = u.id 
                          ORDER BY r.start_date');
    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Bookings - Admin - Picasso's Cabins</title>
    <link rel="icon" href="../assets/images/logo.png" type="image/png">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .actions {
            display: flex;
            gap: 5px;
        }
        .btn {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            font-size: 12px;
        }
        .btn-edit {
            background-color: #007bff;
            color: white;
        }
        .btn-delete {
            background-color: #dc3545;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 5px;
            width: 400px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .success {
            color: green;
            margin-bottom: 15px;
        }
        .error {
            color: red;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>Picasso's Cabins - Admin</h1>
            </div>
            <ul>
                <li><a href="../index.php">Home</a></li>
                <li><a href="../dashboard.php">My Bookings</a></li>
                <li><a href="../admin/view_bookings.php">All Bookings</a></li>
                <li><a href="../admin/manage_cabins.php">Manage Cabins</a></li>
                <li><a href="../logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <h2>All Reservations</h2>
        
        <?php if (isset($success_message)): ?>
            <div class="success"><?php echo htmlspecialchars($success_message); ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if (empty($bookings)): ?>
            <p>No bookings in the system yet.</p>
        <?php else: ?>
            <div class="bookings-list">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Cabin</th>
                            <th>Location</th>
                            <th>Check-in</th>
                            <th>Check-out</th>
                            <th>Nights</th>
                            <th>Total Price</th>
                            <th>Booked On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($bookings as $booking): 
                            // Calculate number of nights and total price
                            $start = new DateTime($booking['start_date']);
                            $end = new DateTime($booking['end_date']);
                            $nights = $end->diff($start)->days;
                            $total_price = $nights * $booking['price_per_night'];
                        ?>
                            <tr>
                                <td><?php echo $booking['id']; ?></td>
                                <td>
                                    <?php echo htmlspecialchars($booking['user_name']); ?>
                                    <br>
                                    <small><?php echo htmlspecialchars($booking['user_email']); ?></small>
                                </td>
                                <td>
                                    <a href="../cabins/view.php?id=<?php echo $booking['cabin_id']; ?>">
                                        <?php echo htmlspecialchars($booking['title']); ?>
                                    </a>
                                </td>
                                <td><?php echo htmlspecialchars($booking['location']); ?></td>
                                <td><?php echo date('M d, Y', strtotime($booking['start_date'])); ?></td>
                                <td><?php echo date('M d, Y', strtotime($booking['end_date'])); ?></td>
                                <td><?php echo $nights; ?></td>
                                <td>$<?php echo number_format($total_price, 2); ?></td>
                                <td><?php echo date('M d, Y', strtotime($booking['created_at'])); ?></td>
                                <td>
                                    <div class="actions">
                                        <button class="btn btn-edit" onclick="openEditModal(<?php echo $booking['id']; ?>, '<?php echo $booking['start_date']; ?>', '<?php echo $booking['end_date']; ?>')">Edit</button>
                                        <button class="btn btn-delete" onclick="confirmDelete(<?php echo $booking['id']; ?>)">Delete</button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <h3>Edit Reservation</h3>
            <form method="POST">
                <input type="hidden" id="edit_reservation_id" name="reservation_id">
                <div class="form-group">
                    <label for="edit_start_date">Check-in Date:</label>
                    <input type="date" id="edit_start_date" name="start_date" required>
                </div>
                <div class="form-group">
                    <label for="edit_end_date">Check-out Date:</label>
                    <input type="date" id="edit_end_date" name="end_date" required>
                </div>
                <div class="form-group">
                    <button type="submit" name="update_reservation" class="btn btn-edit">Update Reservation</button>
                    <button type="button" onclick="closeEditModal()" class="btn">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Form (hidden) -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" id="delete_reservation_id" name="reservation_id">
        <input type="hidden" name="delete_reservation" value="1">
    </form>

    <script>
        function openEditModal(id, startDate, endDate) {
            document.getElementById('edit_reservation_id').value = id;
            document.getElementById('edit_start_date').value = startDate;
            document.getElementById('edit_end_date').value = endDate;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        function confirmDelete(id) {
            if (confirm('Are you sure you want to delete this reservation? This action cannot be undone.')) {
                document.getElementById('delete_reservation_id').value = id;
                document.getElementById('deleteForm').submit();
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }
    </script>
</body>
</html>