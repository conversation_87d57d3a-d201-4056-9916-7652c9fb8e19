<?php
require_once 'includes/db.php';
session_start();

$error = '';
$success = '';

// Get cabin ID from URL
$cabin_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

if (!$cabin_id) {
    header('Location: index.php');
    exit();
}

// Fetch cabin details
try {
    $stmt = $pdo->prepare('SELECT * FROM cabins WHERE id = ?');
    $stmt->execute([$cabin_id]);
    $cabin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cabin) {
        header('Location: index.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($cabin['title']); ?> - <PERSON>'s Cabins</title>
    <link rel="icon" href="assets/images/logo.png" type="image/png">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>Picasso's Cabins</h1>
            </div>
            <ul>
                <li><a href="index.php">Home</a></li>
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li><a href="dashboard.php">My Bookings</a></li>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                        <li><a href="admin/view_bookings.php">Admin</a></li>
                    <?php endif; ?>
                    <li><a href="logout.php">Logout</a></li>
                <?php else: ?>
                    <li><a href="login.php">Login</a></li>
                    <li><a href="register.php">Register</a></li>
                <?php endif; ?>
            </ul>
        </nav>
    </header>

    <div class="container">
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <div class="cabin-details">
            <div class="cabin-image">
                <?php if (!empty($cabin['image'])): ?>
                    <img src="assets/images/<?php echo htmlspecialchars($cabin['image']); ?>" alt="<?php echo htmlspecialchars($cabin['title']); ?>" class="responsive-image">
                <?php else: ?>
                    <img src="assets/images/default-cabin.jpg" alt="Default cabin image" class="responsive-image">
                <?php endif; ?>
            </div>
            
            <div class="cabin-info">
                <h2><?php echo htmlspecialchars($cabin['title']); ?></h2>
                <p class="cabin-location"><?php echo htmlspecialchars($cabin['location']); ?></p>
                <p class="cabin-price" id="price-per-night" data-price="<?php echo $cabin['price_per_night']; ?>">$<?php echo htmlspecialchars($cabin['price_per_night']); ?> per night</p>
                <div class="cabin-description">
                    <p><?php echo nl2br(htmlspecialchars($cabin['description'])); ?></p>
                </div>
            </div>
        </div>
        
        <div class="booking-section">
            <h3>Book This Cabin</h3>
            
            <form id="booking-form" action="book.php" method="POST">
                <input type="hidden" name="cabin_id" value="<?php echo $cabin_id; ?>">
                
                <?php if (!isset($_SESSION['user_id'])): ?>
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="name">Name:</label>
                        <input type="text" id="name" name="name">
                    </div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label for="check_in">Check-in Date:</label>
                    <input type="date" id="check_in" name="check_in" required>
                </div>
                
                <div class="form-group">
                    <label for="check_out">Check-out Date:</label>
                    <input type="date" id="check_out" name="check_out" required>
                </div>
                
                <div class="form-group">
                    <label for="guests">Number of Guests:</label>
                    <select id="guests" name="guests" required>
                        <?php for ($i = 1; $i <= 6; $i++): ?>
                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="extra_bed"> Add extra bed
                    </label>
                </div>
                
                <div class="form-group">
                    <p>Total: $<span id="total-price">0.00</span></p>
                </div>
                
                <div class="form-group">
                    <button type="submit">Book Now</button>
                </div>
            </form>
         </div>
    </div>

    <script src="assets/js/app.js"></script>
</body>
</html>