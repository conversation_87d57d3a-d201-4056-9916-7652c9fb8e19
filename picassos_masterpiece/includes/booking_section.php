<!-- Booking Section -->
<div class="col" id="booking">
    <div class="booking-card sticky-top" style="top: 20px;">
        <h3 class="mb-3">Book your stay</h3>

        <?php
        // Display booking errors if any
        if (isset($_SESSION['booking_errors']) && !empty($_SESSION['booking_errors'])) {
            echo '<div class="alert alert-danger mb-3">';
            foreach ($_SESSION['booking_errors'] as $error) {
                echo '<p class="mb-0"><i class="bi bi-exclamation-circle-fill me-2"></i>' . htmlspecialchars($error) . '</p>';
            }
            echo '</div>';
            // Clear errors after displaying
            unset($_SESSION['booking_errors']);
        }
        ?>

        <div class="price-info mb-3">
            <span class="h4 fw-bold">$299</span> <span class="text-muted">/ night</span>
        </div>
        <form action="../book.php" method="post">
            <input type="hidden" name="cabin_id" value="3">
            <input type="hidden" id="check_in" name="check_in" required>
            <input type="hidden" id="check_out" name="check_out" required>

            <?php if (!isset($_SESSION['user_id'])): ?>
            <div class="mb-3">
                <label for="name" class="form-label">Your Name</label>
                <input type="text" class="form-control" id="name" name="name">
            </div>
            <?php endif; ?>

            <!-- Calendar Section -->
            <div class="mb-3">
                <p class="mb-2">Select your dates:</p>
                <div class="calendar-container">
                    <div class="date-picker-wrapper">
                        <input type="text" id="date-range-picker" class="form-control" placeholder="Select check-in and check-out dates" readonly>
                    </div>
                    <div class="selected-dates mt-2">
                        <div id="selected-dates-display" class="small text-muted">Select check-in and check-out dates</div>
                    </div>
                </div>
            </div>

            <!-- Flatpickr CSS -->
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
            <!-- Flatpickr Theme -->
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">

            <div class="mb-3">
                <label for="guests" class="form-label">Guests</label>
                <input type="number" class="form-control" id="guests" name="guests" min="1" max="6" value="2" required>
                <div class="form-text">Maximum 6 guests allowed</div>
            </div>

            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                <div class="form-text">We'll send your booking confirmation here</div>
            </div>

            <!-- Coupon Section -->
            <div class="mb-3">
                <label for="coupon" class="form-label">Coupon Code</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="coupon" name="coupon" placeholder="Enter coupon code">
                    <button type="button" class="btn btn-outline-secondary" id="apply-coupon">Apply</button>
                </div>
                <div id="coupon-message" class="form-text"></div>
            </div>

            <!-- Price Details Section -->
            <div class="price-details mb-3">
                <h5 class="mb-3">Price Details</h5>
                <div class="d-flex justify-content-between mb-2">
                    <div>$299 × <span id="num-nights">0</span> nights</div>
                    <div>$<span id="subtotal">0</span></div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <div>Cleaning fee</div>
                    <div>$75</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <div>Service fee</div>
                    <div>$<span id="service-fee">0</span></div>
                </div>
                <div class="d-flex justify-content-between mb-2" id="coupon-discount-row" style="display: none;">
                    <div>Coupon discount (20%)</div>
                    <div id="coupon-discount" class="text-success">-$0</div>
                </div>
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <div>Total</div>
                    <div>$<span id="total-price">0</span></div>
                </div>
            </div>

            <button type="submit" class="btn btn-primary w-100 mb-3">Reserve</button>
            <p class="text-center text-muted small">You won't be charged yet</p>
        </form>
    </div>
</div>

<!-- Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get unavailable dates from the database
        <?php
        // Connect to the database
        require_once($_SERVER['DOCUMENT_ROOT'] . '/includes/db.php');

        // Get the cabin ID from the hidden input
        $cabin_id = 3; // This should match the hidden input value

        // Query to get all reservations for this cabin
        $query = "SELECT start_date, end_date FROM reservations WHERE cabin_id = :cabin_id";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':cabin_id', $cabin_id, PDO::PARAM_INT);
        $stmt->execute();

        $bookedDates = [];

        // Process each reservation to get all dates between start and end
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $start = new DateTime($row['start_date']);
            $end = new DateTime($row['end_date']);
            $interval = new DateInterval('P1D'); // 1 day interval
            $dateRange = new DatePeriod($start, $interval, $end);

            // Add each date in the range to the bookedDates array
            foreach ($dateRange as $date) {
                $bookedDates[] = $date->format('Y-m-d');
            }
            // Add the end date as well (not included in the range)
            $bookedDates[] = $end->format('Y-m-d');
        }

        // Convert PHP array to JavaScript array
        echo "const unavailableDates = " . json_encode($bookedDates) . ";";
        ?>

        // Initialize date picker
        const fp = flatpickr("#date-range-picker", {
            mode: "range",
            minDate: "today",
            dateFormat: "Y-m-d",
            disable: unavailableDates,
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length === 2) {
                    const checkIn = selectedDates[0];
                    const checkOut = selectedDates[1];

                    // Set hidden input values
                    document.getElementById('check_in').value = formatDate(checkIn);
                    document.getElementById('check_out').value = formatDate(checkOut);

                    // Display selected dates
                    const nights = calculateNights(checkIn, checkOut);
                    document.getElementById('selected-dates-display').textContent =
                        `${formatDateDisplay(checkIn)} to ${formatDateDisplay(checkOut)} · ${nights} nights`;

                    // Update price calculation
                    updatePriceCalculation(nights);
                }
            }
        });

        // Format date for input value (YYYY-MM-DD)
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // Format date for display (Month Day, Year)
        function formatDateDisplay(date) {
            const options = { month: 'short', day: 'numeric' };
            return date.toLocaleDateString('en-US', options);
        }

        // Calculate number of nights between two dates
        function calculateNights(checkIn, checkOut) {
            const timeDiff = checkOut.getTime() - checkIn.getTime();
            return Math.ceil(timeDiff / (1000 * 3600 * 24));
        }

        // Global variables for coupon
        let couponApplied = false;
        let couponDiscount = 0;

        // Update price calculation based on number of nights
        function updatePriceCalculation(nights) {
            const pricePerNight = 299;
            const subtotal = pricePerNight * nights;
            const serviceFee = Math.round(subtotal * 0.12);
            const cleaningFee = 75;
            let total = subtotal + serviceFee + cleaningFee;

            // Apply coupon discount if applicable
            if (couponApplied && nights > 0) {
                couponDiscount = Math.round(total * 0.2); // 20% discount
                total -= couponDiscount;
                document.getElementById('coupon-discount').textContent = `-$${couponDiscount}`;
                document.getElementById('coupon-discount-row').style.display = 'flex';
            } else {
                couponDiscount = 0;
                document.getElementById('coupon-discount-row').style.display = 'none';
            }

            document.getElementById('num-nights').textContent = nights;
            document.getElementById('subtotal').textContent = subtotal;
            document.getElementById('service-fee').textContent = serviceFee;
            document.getElementById('total-price').textContent = total;
        }

        // Coupon functionality
        document.getElementById('apply-coupon').addEventListener('click', function() {
            const couponInput = document.getElementById('coupon');
            const couponMessage = document.getElementById('coupon-message');
            const couponCode = couponInput.value.trim().toUpperCase();

            if (couponCode === 'SAVE20') {
                if (!couponApplied) {
                    couponApplied = true;
                    couponMessage.innerHTML = '<span class="text-success"><i class="bi bi-check-circle-fill me-1"></i>Coupon applied! 20% discount</span>';
                    couponMessage.style.backgroundColor = '#d4edda';
                    couponMessage.style.padding = '8px';
                    couponMessage.style.borderRadius = '4px';
                    couponMessage.style.border = '1px solid #c3e6cb';

                    // Recalculate prices
                    const nights = parseInt(document.getElementById('num-nights').textContent) || 0;
                    if (nights > 0) {
                        updatePriceCalculation(nights);
                    }
                } else {
                    couponMessage.innerHTML = '<span class="text-info">Coupon already applied</span>';
                }
            } else if (couponCode === '') {
                couponMessage.innerHTML = '<span class="text-muted">Please enter a coupon code</span>';
            } else {
                couponMessage.innerHTML = '<span class="text-danger">Invalid coupon code</span>';
            }
        });

        // Remove coupon when input is cleared
        document.getElementById('coupon').addEventListener('input', function() {
            if (this.value.trim() === '' && couponApplied) {
                couponApplied = false;
                couponDiscount = 0;
                document.getElementById('coupon-message').innerHTML = '';
                document.getElementById('coupon-message').style.backgroundColor = '';
                document.getElementById('coupon-message').style.padding = '';
                document.getElementById('coupon-message').style.borderRadius = '';
                document.getElementById('coupon-message').style.border = '';

                // Recalculate prices
                const nights = parseInt(document.getElementById('num-nights').textContent) || 0;
                if (nights > 0) {
                    updatePriceCalculation(nights);
                }
            }
        });
    });
</script>