// JavaScript for Cabin Reservation System

// Date validation for booking form
document.addEventListener('DOMContentLoaded', function() {
    // Get booking form elements if they exist
    const bookingForm = document.getElementById('booking-form');
    
    if (bookingForm) {
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        // Set minimum date to today for both inputs
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const todayString = `${yyyy}-${mm}-${dd}`;
        
        startDateInput.setAttribute('min', todayString);
        endDateInput.setAttribute('min', todayString);
        
        // Validate start date is before end date
        startDateInput.addEventListener('change', function() {
            endDateInput.setAttribute('min', startDateInput.value);
            
            // If end date is before start date, reset it
            if (endDateInput.value && endDateInput.value < startDateInput.value) {
                endDateInput.value = startDateInput.value;
            }
        });
        
        // Function to calculate and update total price
        function updateTotalPrice() {
            if (!startDateInput.value || !endDateInput.value) {
                return;
            }
            
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            
            if (startDate > endDate) {
                return;
            }
            
            const nights = Math.round((endDate - startDate) / (1000 * 60 * 60 * 24));
            
            if (nights < 1) {
                return;
            }
            
            const pricePerNight = document.getElementById('price-per-night');
            const totalPrice = document.getElementById('total-price');
            
            if (pricePerNight && totalPrice) {
                const price = parseFloat(pricePerNight.dataset.price);
                totalPrice.textContent = (price * nights).toFixed(2);
            }
        }
        
        // Update price when dates change
        startDateInput.addEventListener('change', updateTotalPrice);
        endDateInput.addEventListener('change', updateTotalPrice);
        
        // Form submission validation
        bookingForm.addEventListener('submit', function(e) {
            if (!startDateInput.value || !endDateInput.value) {
                e.preventDefault();
                alert('Please select both start and end dates');
                return false;
            }
            
            if (startDateInput.value > endDateInput.value) {
                e.preventDefault();
                alert('End date must be after start date');
                return false;
            }
            
            // Calculate number of nights
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            const nights = Math.round((endDate - startDate) / (1000 * 60 * 60 * 24));
            
            if (nights < 1) {
                e.preventDefault();
                alert('Booking must be for at least one night');
                return false;
            }
            
            // Update total price (already done by the change event, but just to be sure)
            updateTotalPrice();
        });
    }
});