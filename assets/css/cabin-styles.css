/* Custom styles for cabin listing page */

/* Typography enhancements */
.header-content h2 {
    font-family: 'Raleway', sans-serif;
    font-weight: 700;
    letter-spacing: 1px;
}

.header-content p {
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* Enhanced header styles */
.video-header {
    position: relative;
    height: 70vh;
    min-height: 500px;
    overflow: hidden;

    @media (max-width: 768px) {
        height: 100vh;
    }
}

.video-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.video-bg video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.8);
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6));
    z-index: 2;
}

.header-content {
    position: relative;
    z-index: 20;
    padding-top: 1rem;
}

.header-content h2 {
    font-size: 4rem;
    font-weight: 700;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.6);
}

.header-content p {
    font-size: 1.6rem;
    max-width: 700px;
    margin: 0 auto 30px;
    text-shadow: 1px 1px 4px rgba(0,0,0,0.8);
}

/* Navigation styles */
.modern-nav2 {
    padding: 15px 0;
}

.modern-nav2 .logo h1 {
    font-family: 'Raleway', sans-serif;
    font-weight: 700;
    letter-spacing: 1px;
}

.modern-nav2 ul li a {
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.modern-nav .logo h1 {
    font-weight: 700;
    font-size: 2.2rem;
    color: #fff;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.modern-nav ul li a {
    position: relative;
    padding: 8px 15px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.modern-nav ul li a:hover {
    color: #3498db;
}

.modern-nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background: #3498db;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    transition: width 0.3s;
}

.modern-nav ul li a:hover::after {
    width: 70%;
}

.explore-btn {
    display: inline-block;
    background-color: #e67e22;
    color: white;
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.explore-btn:hover {
    background: #2980b9;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
    color: white;
}

/* Cabin card styles */
.cabin-card {
    transition: all 0.3s ease;
    border: none;
}

.cabin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

.cabin-image-container {
    height: 220px;
    overflow: hidden;
}

.cabin-image-container img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.cabin-card:hover .cabin-image-container img {
    transform: scale(1.05);
}

.cabin-price-badge {
    position: absolute;
    top: 15px;
    right: 15px;
}

.cabin-location {
    color: #666;
    font-size: 0.95rem;
}

.cabin-features .badge {
    font-weight: 500;
    padding: 6px 12px;
}

.cabin-description {
    color: #555;
    font-size: 0.95rem;
    line-height: 1.5;
}