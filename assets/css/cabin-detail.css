/* Custom styles for cabin detail page */

/* Cabin gallery styles */
.cabin-gallery img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.cabin-gallery .main-image img {
    height: 510px;
}

.cabin-gallery img:hover {
    transform: scale(1.02);
}

/* Booking card styles */
.booking-card {
    background-color: #fff;
    border-radius: 10px;
    transition: all 0.3s ease;
}

/* Review styles */
.review {
    transition: all 0.3s ease;
}

.review:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Modal gallery styles */
#galleryModal .modal-content {
    border-radius: 15px;
    overflow: hidden;
}

#galleryModal .modal-body img {
    height: 200px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

#galleryModal .modal-body img:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cabin-gallery .main-image img {
        height: 300px;
    }
    
    .cabin-gallery img {
        height: 150px;
    }
    
    #galleryModal .modal-body img {
        height: 150px;
    }
}