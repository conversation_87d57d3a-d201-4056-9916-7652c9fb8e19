<?php
session_start();

// Include database connection
require_once 'includes/db.php';

// Function to get database connection
function connectDB() {
    global $pdo;
    return $pdo;
}

// Function to check if dates are available
function areDatesAvailable($conn, $cabin_id, $start_date, $end_date) {
    $query = "SELECT * FROM reservations
              WHERE cabin_id = :cabin_id
              AND (
                  (start_date <= :end_date AND end_date >= :start_date)
              )";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':cabin_id', $cabin_id, PDO::PARAM_INT);
    $stmt->bindParam(':start_date', $start_date);
    $stmt->bindParam(':end_date', $end_date);
    $stmt->execute();

    return $stmt->rowCount() === 0; // If no overlapping reservations found, dates are available
}

// Function to find user by email
function getUserByEmail($conn, $email) {
    $query = "SELECT * FROM users WHERE email = :email LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Function to create a new user
function createUser($conn, $email, $name = '') {
    // Generate a random password (in a real app, you'd want to implement a proper registration flow)
    $random_password = bin2hex(random_bytes(8));
    $hashed_password = password_hash($random_password, PASSWORD_DEFAULT);

    // If name is empty, use the part of email before @
    if (empty($name)) {
        $name = explode('@', $email)[0];
    }

    $query = "INSERT INTO users (name, email, password) VALUES (:name, :email, :password)";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':password', $hashed_password);
    $stmt->execute();

    return $conn->lastInsertId();
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $cabin_id = isset($_POST['cabin_id']) ? intval($_POST['cabin_id']) : 0;
    $check_in = isset($_POST['check_in']) ? $_POST['check_in'] : '';
    $check_out = isset($_POST['check_out']) ? $_POST['check_out'] : '';
    $guests = isset($_POST['guests']) ? intval($_POST['guests']) : 0;
    $extra_bed = isset($_POST['extra_bed']) ? 1 : 0;
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $coupon = isset($_POST['coupon']) ? trim($_POST['coupon']) : '';

    // Validate input
    $errors = [];

    // Validate coupon
    $coupon_applied = false;
    $coupon_discount = 0;
    if (!empty($coupon)) {
        if (strtoupper($coupon) === 'SAVE20') {
            $coupon_applied = true;
        } else {
            $errors[] = "Invalid coupon code.";
        }
    }

    if ($cabin_id <= 0) {
        $errors[] = "Invalid cabin selection.";
    }

    if (empty($check_in) || empty($check_out)) {
        $errors[] = "Please select check-in and check-out dates.";
    } else {
        // Validate date format (YYYY-MM-DD)
        $date_pattern = '/^\d{4}-\d{2}-\d{2}$/';
        if (!preg_match($date_pattern, $check_in) || !preg_match($date_pattern, $check_out)) {
            $errors[] = "Invalid date format.";
        } else {
            // Check if check-out is after check-in
            $check_in_date = new DateTime($check_in);
            $check_out_date = new DateTime($check_out);

            if ($check_in_date >= $check_out_date) {
                $errors[] = "Check-out date must be after check-in date.";
            }
        }
    }

    if ($guests <= 0 || $guests > 6) {
        $errors[] = "Please select a valid number of guests (1-6).";
    }

    // Validate email
    if (empty($email)) {
        $errors[] = "Please provide an email address.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Please provide a valid email address.";
    }

    // If no validation errors, proceed with booking
    if (empty($errors)) {
        try {
            $conn = connectDB();

            // Check if the selected dates are available
            if (areDatesAvailable($conn, $cabin_id, $check_in, $check_out)) {
                // If user is logged in, use their user_id
                if (isset($_SESSION['user_id'])) {
                    $user_id = $_SESSION['user_id'];
                } else {
                    // Check if user exists with the provided email
                    $user = getUserByEmail($conn, $email);

                    // If user doesn't exist, create a new one
                    if (!$user) {
                        $user_id = createUser($conn, $email, $name);
                    } else {
                        $user_id = $user['id'];
                    }
                }

                // Insert reservation
                $query = "INSERT INTO reservations (user_id, cabin_id, start_date, end_date)
                          VALUES (:user_id, :cabin_id, :start_date, :end_date)";

                $stmt = $conn->prepare($query);
                $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                $stmt->bindParam(':cabin_id', $cabin_id, PDO::PARAM_INT);
                $stmt->bindParam(':start_date', $check_in);
                $stmt->bindParam(':end_date', $check_out);

                if ($stmt->execute()) {
                    // Reservation successful
                    $reservation_id = $conn->lastInsertId();

                    // Store success message in session
                    $_SESSION['booking_success'] = true;
                    $_SESSION['reservation_id'] = $reservation_id;
                    $_SESSION['cabin_id'] = $cabin_id;
                    $_SESSION['check_in'] = $check_in;
                    $_SESSION['check_out'] = $check_out;
                    $_SESSION['guests'] = $guests;
                    $_SESSION['extra_bed'] = $extra_bed;
                    $_SESSION['email'] = $email;
                    $_SESSION['name'] = $name;
                    $_SESSION['coupon_applied'] = $coupon_applied;
                    $_SESSION['coupon_code'] = $coupon_applied ? $coupon : '';

                    // Redirect to confirmation page
                    header("Location: booking_confirmation.php");
                    exit;
                } else {
                    $errors[] = "Failed to create reservation. Please try again.";
                }
            } else {
                $errors[] = "Sorry, the selected dates are not available. Please choose different dates.";
            }
        } catch(PDOException $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }

    // If there were errors, store them in session and redirect back to the cabin page
    if (!empty($errors)) {
        print_r($errors);
        $_SESSION['booking_errors'] = $errors;

        // Redirect back to the cabin page
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';
        header("Location: $referer");
        exit;
    }
} else {
    // If not a POST request, redirect to homepage
    header("Location: index.php");
    exit;
}