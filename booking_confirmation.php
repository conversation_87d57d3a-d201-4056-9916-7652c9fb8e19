<?php
session_start();

// Include mail functions
require_once 'includes/mail.php';

// Check if there's a successful booking in the session
if (!isset($_SESSION['booking_success']) || $_SESSION['booking_success'] !== true) {
    // No successful booking found, redirect to homepage
    header("Location: index.php");
    exit;
}

// Get booking details from session
$reservation_id = $_SESSION['reservation_id'] ?? 'Unknown';
$check_in = $_SESSION['check_in'] ?? 'Unknown';
$check_out = $_SESSION['check_out'] ?? 'Unknown';
$guests = $_SESSION['guests'] ?? 0;
$extra_bed = $_SESSION['extra_bed'] ?? 0;

// Format dates for display
$check_in_formatted = date('F j, Y', strtotime($check_in));
$check_out_formatted = date('F j, Y', strtotime($check_out));

// Calculate number of nights
$check_in_date = new DateTime($check_in);
$check_out_date = new DateTime($check_out);
$interval = $check_in_date->diff($check_out_date);
$nights = $interval->days;

// Get user details
$email = $_SESSION['email'] ?? '';
$name = $_SESSION['name'] ?? 'Customer';

// Get coupon details
$coupon_applied = $_SESSION['coupon_applied'] ?? false;
$coupon_code = $_SESSION['coupon_code'] ?? '';

// Clear the booking session data to prevent reusing the confirmation page
// We'll keep the data for this page load, but clear it for future requests
$_SESSION['booking_success'] = false;

// Get cabin details (in a real app, this would come from the database)
$cabin_id = $_SESSION['cabin_id'] ?? 1;
$cabin_names = [
    1 => "Picasso's Inspiration",
    2 => "Picasso's Notebook",
    3 => "Picasso's Masterpiece"
];
$cabin_name = $cabin_names[$cabin_id] ?? 'Unknown Cabin';

// Calculate pricing based on cabin
$cabin_prices = [
    1 => ['nightly' => 199, 'cleaning' => 50, 'service' => 45],
    2 => ['nightly' => 249, 'cleaning' => 75, 'service' => 45],
    3 => ['nightly' => 299, 'cleaning' => 75, 'service' => 0] // Service fee calculated as 12% of subtotal
];

$pricing = $cabin_prices[$cabin_id] ?? $cabin_prices[1];
$subtotal = $pricing['nightly'] * $nights;
$cleaning_fee = $pricing['cleaning'];

// Calculate service fee (for cabin 3, it's 12% of subtotal)
if ($cabin_id == 3) {
    $service_fee = round($subtotal * 0.12);
} else {
    $service_fee = $pricing['service'];
}

$extra_bed_fee = $extra_bed ? 25 : 0;
$total_before_discount = $subtotal + $cleaning_fee + $service_fee + $extra_bed_fee;

// Apply coupon discount
$coupon_discount = 0;
if ($coupon_applied) {
    $coupon_discount = round($total_before_discount * 0.2); // 20% discount
}

$final_total = $total_before_discount - $coupon_discount;

// Send confirmation email if email is available
if (!empty($email)) {
    send_booking_confirmation_email(
        $email,
        $name,
        $reservation_id,
        $cabin_name,
        $check_in,
        $check_out,
        $guests,
        $extra_bed
    );

    // Send notification to admin
    send_admin_booking_notification(
        $reservation_id,
        $cabin_name,
        $check_in,
        $check_out,
        $email,
        $name
    );
}

// Include header
$page_title = "Booking Confirmation";
include 'includes/header.php';
?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        <h1 class="mt-3">Booking Confirmed!</h1>
                        <p class="lead text-muted">Your reservation has been successfully processed.</p>
                    </div>

                    <div class="booking-details">
                        <h3 class="mb-4">Reservation Details</h3>

                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Confirmation Number:</div>
                            <div class="col-md-8"><?php echo $reservation_id; ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Cabin:</div>
                            <div class="col-md-8"><?php echo $cabin_name; ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Check-in:</div>
                            <div class="col-md-8"><?php echo $check_in_formatted; ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Check-out:</div>
                            <div class="col-md-8"><?php echo $check_out_formatted; ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Duration:</div>
                            <div class="col-md-8"><?php echo $nights; ?> night<?php echo $nights > 1 ? 's' : ''; ?></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Guests:</div>
                            <div class="col-md-8"><?php echo $guests; ?> person<?php echo $guests > 1 ? 's' : ''; ?></div>
                        </div>

                        <?php if ($extra_bed): ?>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Extra:</div>
                            <div class="col-md-8">Extra bed included</div>
                        </div>
                        <?php endif; ?>

                        <?php if ($coupon_applied): ?>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Coupon Applied:</div>
                            <div class="col-md-8">
                                <span class="badge bg-success"><?php echo strtoupper($coupon_code); ?></span>
                                <small class="text-success ms-2">20% discount applied</small>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <hr class="my-4">

                    <!-- Price Breakdown -->
                    <div class="price-breakdown">
                        <h4 class="mb-3">Price Breakdown</h4>

                        <div class="row mb-2">
                            <div class="col-8">$<?php echo $pricing['nightly']; ?> × <?php echo $nights; ?> night<?php echo $nights > 1 ? 's' : ''; ?></div>
                            <div class="col-4 text-end">$<?php echo number_format($subtotal, 2); ?></div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-8">Cleaning fee</div>
                            <div class="col-4 text-end">$<?php echo number_format($cleaning_fee, 2); ?></div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-8">Service fee</div>
                            <div class="col-4 text-end">$<?php echo number_format($service_fee, 2); ?></div>
                        </div>

                        <?php if ($extra_bed): ?>
                        <div class="row mb-2">
                            <div class="col-8">Extra bed</div>
                            <div class="col-4 text-end">$<?php echo number_format($extra_bed_fee, 2); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if ($coupon_applied): ?>
                        <div class="row mb-2 text-success">
                            <div class="col-8">Coupon discount (20%)</div>
                            <div class="col-4 text-end">-$<?php echo number_format($coupon_discount, 2); ?></div>
                        </div>
                        <?php endif; ?>

                        <hr>

                        <div class="row fw-bold fs-5">
                            <div class="col-8">Total</div>
                            <div class="col-4 text-end">$<?php echo number_format($final_total, 2); ?></div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-4">A confirmation email has been sent to your email address.</p>
                        <a href="index.php" class="btn btn-primary">Return to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>