<?php
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Ensure admin access
requireAdmin();

$error = '';
$success = '';

// Handle cabin deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $cabin_id = $_GET['delete'];
    
    try {
        // Check if cabin has any reservations
        $stmt = $pdo->prepare('SELECT COUNT(*) FROM reservations WHERE cabin_id = ?');
        $stmt->execute([$cabin_id]);
        $has_reservations = $stmt->fetchColumn() > 0;
        
        if ($has_reservations) {
            $error = 'Cannot delete cabin with existing reservations';
        } else {
            // Delete the cabin
            $stmt = $pdo->prepare('DELETE FROM cabins WHERE id = ?');
            $stmt->execute([$cabin_id]);
            $success = 'Cabin deleted successfully';
        }
    } catch (PDOException $e) {
        $error = 'Database error: ' . $e->getMessage();
    }
}

// Handle cabin creation/update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $cabin_id = isset($_POST['cabin_id']) ? filter_input(INPUT_POST, 'cabin_id', FILTER_VALIDATE_INT) : null;
    $title = filter_input(INPUT_POST, 'title', FILTER_SANITIZE_STRING);
    $description = filter_input(INPUT_POST, 'description', FILTER_SANITIZE_STRING);
    $location = filter_input(INPUT_POST, 'location', FILTER_SANITIZE_STRING);
    $price = filter_input(INPUT_POST, 'price', FILTER_VALIDATE_FLOAT);
    $image = filter_input(INPUT_POST, 'image', FILTER_SANITIZE_STRING);
    
    if (empty($title) || empty($location) || !$price) {
        $error = 'Please fill in all required fields';
    } else {
        try {
            if ($cabin_id) {
                // Update existing cabin
                $stmt = $pdo->prepare('UPDATE cabins SET title = ?, description = ?, location = ?, price_per_night = ?, image = ? WHERE id = ?');
                $stmt->execute([$title, $description, $location, $price, $image, $cabin_id]);
                $success = 'Cabin updated successfully';
            } else {
                // Create new cabin
                $stmt = $pdo->prepare('INSERT INTO cabins (title, description, location, price_per_night, image) VALUES (?, ?, ?, ?, ?)');
                $stmt->execute([$title, $description, $location, $price, $image]);
                $success = 'Cabin created successfully';
            }
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

// Fetch all cabins for display
try {
    $stmt = $pdo->query('SELECT * FROM cabins ORDER BY id DESC');
    $cabins = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}

// Get cabin for editing if edit parameter is set
$edit_cabin = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $pdo->prepare('SELECT * FROM cabins WHERE id = ?');
        $stmt->execute([$_GET['edit']]);
        $edit_cabin = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $error = 'Database error: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Cabins - Admin - Picasso's Cabins</title>
    <link rel="icon" href="../assets/images/logo.png" type="image/png">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>Picasso's Cabins - Admin</h1>
            </div>
            <ul>
                <li><a href="../index.php">Home</a></li>
                <li><a href="../dashboard.php">My Bookings</a></li>
                <li><a href="view_bookings.php">All Bookings</a></li>
                <li><a href="manage_cabins.php">Manage Cabins</a></li>
                <li><a href="../logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <h2><?php echo $edit_cabin ? 'Edit Cabin' : 'Add New Cabin'; ?></h2>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <?php if ($edit_cabin): ?>
                <input type="hidden" name="cabin_id" value="<?php echo $edit_cabin['id']; ?>">
            <?php endif; ?>
            
            <div class="form-group">
                <label for="title">Title:</label>
                <input type="text" id="title" name="title" value="<?php echo $edit_cabin ? htmlspecialchars($edit_cabin['title']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="location">Location:</label>
                <input type="text" id="location" name="location" value="<?php echo $edit_cabin ? htmlspecialchars($edit_cabin['location']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="price">Price per Night ($):</label>
                <input type="number" id="price" name="price" step="0.01" min="0" value="<?php echo $edit_cabin ? htmlspecialchars($edit_cabin['price_per_night']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="image">Image Filename:</label>
                <input type="text" id="image" name="image" value="<?php echo $edit_cabin ? htmlspecialchars($edit_cabin['image']) : ''; ?>">
                <small>Enter the filename only, e.g. "cabin1.jpg"</small>
            </div>
            
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea id="description" name="description" rows="5"><?php echo $edit_cabin ? htmlspecialchars($edit_cabin['description']) : ''; ?></textarea>
            </div>
            
            <div class="form-group">
                <button type="submit"><?php echo $edit_cabin ? 'Update Cabin' : 'Add Cabin'; ?></button>
                <?php if ($edit_cabin): ?>
                    <a href="manage_cabins.php" class="btn-secondary">Cancel</a>
                <?php endif; ?>
            </div>
        </form>
        
        <h2>Existing Cabins</h2>
        
        <?php if (empty($cabins)): ?>
            <p>No cabins available. Add your first cabin above.</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Location</th>
                        <th>Price/Night</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($cabins as $cabin): ?>
                        <tr>
                            <td><?php echo $cabin['id']; ?></td>
                            <td><?php echo htmlspecialchars($cabin['title']); ?></td>
                            <td><?php echo htmlspecialchars($cabin['location']); ?></td>
                            <td>$<?php echo htmlspecialchars($cabin['price_per_night']); ?></td>
                            <td>
                                <a href="manage_cabins.php?edit=<?php echo $cabin['id']; ?>" class="btn-small">Edit</a>
                                <a href="manage_cabins.php?delete=<?php echo $cabin['id']; ?>" class="btn-small btn-danger" onclick="return confirm('Are you sure you want to delete this cabin?')">Delete</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</body>
</html>