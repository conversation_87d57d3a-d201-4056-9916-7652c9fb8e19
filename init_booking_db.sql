
CREATE DATABASE IF NOT EXISTS booking_db;
USE booking_db;

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS cabins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    location VARCHAR(100),
    price_per_night DECIMAL(10,2) NOT NULL,
    image VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS reservations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    cabin_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREI<PERSON><PERSON> KEY (cabin_id) REFERENCES cabins(id)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (name, email, password)
VALUES ('Admin', '<EMAIL>', '$2y$10$ZKjG/NnOLv2n3kBoHtxq8uMZkNkoOcxw3.SGrLaH2smVu7WlO2yQq')
ON DUPLICATE KEY UPDATE email=email;
